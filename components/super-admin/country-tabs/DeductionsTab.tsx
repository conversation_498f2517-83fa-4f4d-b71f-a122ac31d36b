'use client';

import React from 'react';
import { Country } from '@/types/payroll';
import DashboardCard from '@/components/ui/DashboardCard';

interface DeductionsTabProps {
  countryCode: string;
  country: Country;
}

const DeductionsTab: React.FC<DeductionsTabProps> = ({ countryCode, country }) => {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Deductions</h2>
          <p className="text-sm text-gray-600">
            Manage deductions and deduction rates for {country.name}
          </p>
        </div>
        <button
          disabled
          className="bg-gray-400 cursor-not-allowed text-white py-2 px-4 text-sm font-medium rounded-md flex items-center"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          Create Deduction
        </button>
      </div>

      {/* Coming Soon */}
      <DashboardCard title="Deductions Management">
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Coming Soon</h3>
          <p className="text-gray-600 mb-4">
            Deductions management functionality will be available in a future update.
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left">
            <h4 className="font-medium text-blue-900 mb-2">Planned Features:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Create and manage deduction types</li>
              <li>• Set deduction rates and policies</li>
              <li>• Configure mandatory vs optional deductions</li>
              <li>• Define calculation methods</li>
              <li>• Set effective date ranges</li>
            </ul>
          </div>
        </div>
      </DashboardCard>
    </div>
  );
};

export default DeductionsTab;

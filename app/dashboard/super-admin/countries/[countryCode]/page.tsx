import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import CountryDetailContent from '@/components/super-admin/CountryDetailContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

interface CountryDetailPageProps {
  params: {
    countryCode: string;
  };
}

export async function generateMetadata({ params }: CountryDetailPageProps) {
  return {
    title: `${params.countryCode.toUpperCase()} Payroll Management | KaziSync`,
    description: `Manage payroll policies, tax settings, and employee types for ${params.countryCode.toUpperCase()}`,
  };
}

export default function CountryDetailPage({ params }: CountryDetailPageProps) {
  return (
    <ProtectedRoute allowedRoles={['super-admin']}>
      <DashboardLayout>
        <CountryDetailContent countryCode={params.countryCode} />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
